using System.Collections.Generic;

namespace MaoYouJi
{
  [EntitySystemOf(typeof(VipAfkComp))]
  [FriendOf(typeof(VipAfkComp))]
  public static partial class VipAfkCompSys
  {
    [EntitySystem]
    private static void Awake(this VipAfkComp self)
    {
    }

    public static Dictionary<string, string> GetLianGongFangSetting(this VipAfkComp self)
    {
      LianGongAfkSystem lianGongAfkSystem = (LianGongAfkSystem)userCacheInfo.user.vipAfkSystem;
      if (lianGongAfkSystem == null)
      {
        LogUtil.ERROR("lianGongAfkSystem is null", userCacheInfo.user.id);
        return new HashMap<>();
      }
      UserVipFuncInfo vipFuncInfo = userCacheInfo.userSystem.userCountInfo.vipFuncInfo;
      Map<String, String> settingMap = new HashMap<>();
      settingMap.put("remainTime", String.valueOf(lianGongAfkSystem.remainTime));
      settingMap.put("setTime", String.valueOf(lianGongAfkSystem.setTime));
      settingMap.put("enterTime", String.valueOf(lianGongAfkSystem.enterTime));
      settingMap.put("totalExp", String.valueOf(lianGongAfkSystem.totalExp));
      settingMap.put("skillId", lianGongAfkSystem.skillId == null ? "无" : lianGongAfkSystem.skillId.getName());
      long remainTotal = lianGongAfkSystem.isGaoji ? vipFuncInfo.remainGaoJiLianGongFangTime
          : vipFuncInfo.remainLianGongFangTime;
      settingMap.put("remainTotal", String.valueOf(remainTotal));
      return settingMap;
    }

    public Map<String, String> getLvGuanSetting(UserCacheInfo userCacheInfo)
    {
      LvGuanAfkSystem lvGuanAfkSystem = (LvGuanAfkSystem)userCacheInfo.user.vipAfkSystem;
      if (lvGuanAfkSystem == null)
      {
        LogUtil.ERROR("lvGuanAfkSystem is null", userCacheInfo.user.id);
        return new HashMap<>();
      }
      UserVipFuncInfo vipFuncInfo = userCacheInfo.userSystem.userCountInfo.vipFuncInfo;
      Map<String, String> settingMap = new HashMap<>();
      settingMap.put("remainTime", String.valueOf(lvGuanAfkSystem.remainTime));
      settingMap.put("setTime", String.valueOf(lvGuanAfkSystem.setTime));
      settingMap.put("enterTime", String.valueOf(lvGuanAfkSystem.enterTime));
      settingMap.put("totalExp", String.valueOf(lvGuanAfkSystem.totalExp));
      settingMap.put("existTime", String.valueOf(lvGuanAfkSystem.existTime));
      settingMap.put("addZenExp", String.valueOf(lvGuanAfkSystem.addZenExp));
      long remainTotal = lvGuanAfkSystem.isGaoji ? vipFuncInfo.remainGaoJiLvGuanTime
          : vipFuncInfo.remainLvGuanTime;
      settingMap.put("remainTotal", String.valueOf(remainTotal));
      return settingMap;
    }

    public Map<String, String> getAfkSetting(UserCacheInfo userCacheInfo)
    {
      VipAfkSystem vipAfkSystem = userCacheInfo.user.vipAfkSystem;
      if (vipAfkSystem == null)
      {
        LogUtil.ERROR("vipAfkSystem is null", userCacheInfo.user.id);
        return new HashMap<>();
      }
      Map<String, String> settingMap = new HashMap<>();
      settingMap.put("remainTime", String.valueOf(vipAfkSystem.remainTime));
      settingMap.put("setTime", String.valueOf(vipAfkSystem.setTime));
      settingMap.put("enterTime", String.valueOf(vipAfkSystem.enterTime));
      settingMap.put("totalExp", String.valueOf(vipAfkSystem.totalExp));
      return settingMap;
    }
  }
}