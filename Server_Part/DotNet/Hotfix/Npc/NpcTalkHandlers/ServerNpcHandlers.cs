using System.Collections.Generic;
using System.Linq;

namespace MaoYouJi
{
  [Invoke((long)NpcNameEnum.Mu_JinJin)]
  public class MuJinJinHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      ServerNpcTalkListResp resp = data.ServerNpcTalkListResp;
      DaTaoShaActComp daTaoShaActComp = GlobalInfoCache.Instance.daTaoShaActComp;
      if (daTaoShaActComp.State != 0)
        resp.talkList = "can_enter_daTaoSha";
      else
        resp.talkList = "default";
      await ETTask.CompletedTask;
    }
  }

  [Invoke((long)NpcNameEnum.LianGongFang_GuanLiYuan)]
  [FriendOf(typeof(VipAfkComp))]
  [FriendOf(typeof(UserVipFuncInfo))]
  public class LianGongFangGuanLiYuanHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      ServerNpcTalkListResp resp = data.ServerNpcTalkListResp;
      User user = data.User;
      MoveComponent moveComponent = user.GetComponent<MoveComponent>();
      if (!moveComponent.nowPoint.Contains("练功房"))
      {
        resp.SetError("您不在练功房中！");
        return;
      }
      resp.talkList = "show_liangongfang_time";
      resp.talkParamsMap = GetLianGongFangSetting(user);
      await ETTask.CompletedTask;
    }

    private static Dictionary<string, string> GetLianGongFangSetting(User user)
    {
      Dictionary<string, string> result = new();
      VipAfkComp vipAfkSystem = user.GetComponent<VipAfkComp>();
      UserVipFuncInfo userVipFuncInfo = user.GetComponent<UserVipFuncInfo>();
      if (vipAfkSystem != null)
      {
        result["remainTime"] = vipAfkSystem.remainTime.ToString();
      }
      if (userVipFuncInfo != null)
      {
        result["remainGaoJiTime"] = userVipFuncInfo.remainGaoJiLianGongFangTime.ToString();
      }
      return result;
    }
  }

  [Invoke((long)NpcNameEnum.MaoPu_KuaiDi)]
  public class MaoPuKuaiDiHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      ServerNpcTalkListResp resp = data.ServerNpcTalkListResp;
      resp.talkList = "default";
      long mailNum = await data.User.CountMail();
      resp.talkParamsMap = new Dictionary<string, string>
      {
        ["mailNum"] = mailNum.ToString() // TODO: 实现邮件数量统计
      };
      await ETTask.CompletedTask;
    }
  }

  [Invoke((long)NpcNameEnum.LvGuan_GuanLiYuan)]
  [FriendOf(typeof(VipAfkComp))]
  public class LvGuanGuanLiYuanHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      ServerNpcTalkListResp resp = data.ServerNpcTalkListResp;
      User user = data.User;
      resp.talkList = "show_lvguan_time";
      resp.talkParamsMap = GetLvGuanSetting(user);
      await ETTask.CompletedTask;
    }

    private static Dictionary<string, string> GetLvGuanSetting(User user)
    {
      Dictionary<string, string> result = new();
      VipAfkComp vipAfkSystem = user.GetComponent<VipAfkComp>();
      if (vipAfkSystem != null)
      {
        result["remainTime"] = vipAfkSystem.remainTime.ToString();
        result["existTime"] = vipAfkSystem.existTime.ToString();
      }
      return result;
    }
  }

  [Invoke((long)NpcNameEnum.XiaoHe_Shang)]
  [FriendOf(typeof(VipAfkComp))]
  public class XiaoHeShangHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      ServerNpcTalkListResp resp = data.ServerNpcTalkListResp;
      User user = data.User;
      resp.talkList = "show_afk_room_time";
      resp.talkParamsMap = GetAfkSetting(user);
      await ETTask.CompletedTask;
    }

    private static Dictionary<string, string> GetAfkSetting(User user)
    {
      Dictionary<string, string> result = new();
      VipAfkComp vipAfkSystem = user.GetComponent<VipAfkComp>();
      if (vipAfkSystem != null)
      {
        result["remainTime"] = vipAfkSystem.remainTime.ToString();
        result["setTime"] = vipAfkSystem.setTime.ToString();
        result["totalExp"] = vipAfkSystem.totalExp.ToString();
      }
      return result;
    }
  }

  [Invoke((long)NpcNameEnum.Zhang_SanFeng)]
  [FriendOf(typeof(VipAfkComp))]
  public class ZhangSanFengHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      ServerNpcTalkListResp resp = data.ServerNpcTalkListResp;
      User user = data.User;
      resp.talkList = "show_afk_room_time";
      resp.talkParamsMap = GetAfkSetting(user);
      await ETTask.CompletedTask;
    }

    private static Dictionary<string, string> GetAfkSetting(User user)
    {
      Dictionary<string, string> result = new();
      VipAfkComp vipAfkSystem = user.GetComponent<VipAfkComp>();
      if (vipAfkSystem != null)
      {
        result["remainTime"] = vipAfkSystem.remainTime.ToString();
        result["setTime"] = vipAfkSystem.setTime.ToString();
        result["totalExp"] = vipAfkSystem.totalExp.ToString();
      }
      return result;
    }
  }

  [Invoke((long)NpcNameEnum.GongChang_GuanLiYuan)]
  [FriendOf(typeof(VipAfkComp))]
  public class GongChangGuanLiYuanHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      ServerNpcTalkListResp resp = data.ServerNpcTalkListResp;
      User user = data.User;
      resp.talkList = "show_afk_room_time";
      resp.talkParamsMap = GetAfkSetting(user);
      await ETTask.CompletedTask;
    }

    private static Dictionary<string, string> GetAfkSetting(User user)
    {
      Dictionary<string, string> result = new();
      VipAfkComp vipAfkSystem = user.GetComponent<VipAfkComp>();
      if (vipAfkSystem != null)
      {
        result["remainTime"] = vipAfkSystem.remainTime.ToString();
        result["setTime"] = vipAfkSystem.setTime.ToString();
        result["totalExp"] = vipAfkSystem.totalExp.ToString();
      }
      return result;
    }
  }

  [Invoke((long)NpcNameEnum.Wu_DaEr)]
  [FriendOf(typeof(SkillComponent))]
  public class WuDaErHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      ServerNpcTalkListResp resp = data.ServerNpcTalkListResp;
      User user = data.User;
      AttackComponent attackComponent = user.GetComponent<AttackComponent>();
      SkillComponent skillComponent = attackComponent?.GetComponent<SkillComponent>();
      if (skillComponent?.skillMap.ContainsKey(SkillIdEnum.HunPo_CaiJiShu) == true)
      {
        resp.talkList = "default";
      }
      else
      {
        resp.talkList = "show_learn_hunpo_caijishu";
      }
      await ETTask.CompletedTask;
    }
  }

  [Invoke((long)NpcNameEnum.Qiu_Ka)]
  [FriendOf(typeof(BagComponent))]
  [FriendOf(typeof(MapNode))]
  public class QiuKaHandler : AInvokeHandler<ServerNpcProcStruct, ETTask>
  {
    public override async ETTask Handle(ServerNpcProcStruct data)
    {
      ServerNpcTalkListResp resp = data.ServerNpcTalkListResp;
      User user = data.User;
      MapNode mapNode = data.MapNode;
      BagComponent bagComponent = user.GetComponent<BagComponent>();

      if (bagComponent.GetThingInBag<Thing>(ThingNameEnum.YingXing_Yi) != null)
      {
        resp.talkList = "default";
      }
      else
      {
        // 将用户踢出当前地图到附近地图
        if (mapNode.nears.Count > 0)
        {
          var nearPoint = mapNode.nears.First();
          MapNode nearMapNode = GlobalInfoCache.Instance.GetMapNode(mapNode.mapName, nearPoint.pointName);
          if (nearMapNode != null)
          {
            // 移动用户到附近地图
            nearMapNode.PutUserInMapNode(user);

            // 发送聊天消息
            mapNode.SendMessagesToMapUser(new ServerSendChatMsg
            {
              content = $"<u>{user.nickname}</u>被俅卡的守卫厌恶地踢开了",
              chatType = ChatType.Local_Chat
            });

            nearMapNode.SendMessagesToMapUser(new ServerSendChatMsg
            {
              content = $"<u>{user.nickname}</u>被俅卡的守卫赶了出来",
              chatType = ChatType.Local_Chat
            });
          }
        }
        return; // 不设置talkList，直接返回
      }
      await ETTask.CompletedTask;
    }
  }
}